<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      @open="handelOpen"
      width="700px"
    >
      <div v-show="!duplicateList.length && !successList.length">
        导入数据为空
      </div>

      <div v-show="successList.length && !duplicateList.length">
        其中文件可导入
        <el-button type="text" @click="viewFile">{{
          successList.length
        }}</el-button>
        笔 是否继续导入
      </div>
      <div v-show="duplicateList.length">
        <div>
          部分识别成功，其中已识别<el-button type="text">{{
            successList.length
          }}</el-button
          >笔, 其中<el-button type="text">{{ duplicateList.length }}</el-button
          >笔未识别，请重新导入
        </div>
        <el-button type="text" @click="viewFile">文件下载</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-show="duplicateList.length"
          @click="
            dialogVisible = false;
            innerValue = false;
          "
          >关闭</el-button
        >
        <el-button
          v-show="!duplicateList.length && successList.length"
          @click="dialogVisible = false;innerValue = false;"
          >取消</el-button
        >
        <el-button
          v-show="!duplicateList.length && successList.length"
          type="primary"
          @click="exprotSure"
          >导入</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import XLSX from "xlsx-js-style";
import XEUtils from "xe-utils";

export default {
  name: "UploadToast",
  mixins: [vModelMixin],
  props: {
    successList: {
      type: Array,
      default: () => [],
    },
    duplicateList: {
      type: Array,
      default: () => [],
    },
    title: {
      type: Array,
      default: () => [],
    },
    valueArr: {
      type: Array,
      default: () => [],
    },
    fileName: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {};
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},
    handelOpen() {},
    async viewFile() {
      let dataExeclSuccess = XEUtils.clone(this.title, true);
      let dataExeclError = XEUtils.clone(this.title, true);
      this.successList.forEach((item) => {
        let tempItem = [];
        this.valueArr.forEach((item1) => {
          tempItem.push(item[item1] || "");
        });
        dataExeclSuccess.push(tempItem);
      });
      // 创建工作表
      const ws = XLSX.utils.aoa_to_sheet(dataExeclSuccess);
      // 自动调整列宽
      this.autoAdjustColumnWidth(ws, dataExeclSuccess);
      // 创建工作簿并添加工作表
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "识别成功数据");
      if (this.duplicateList.length) {
        this.duplicateList.forEach((item) => {
          let tempItem = [];
          this.valueArr.forEach((item1) => {
            tempItem.push(item[item1] || "");
          });
          dataExeclError.push(tempItem);
        });
        const wsError = XLSX.utils.aoa_to_sheet(dataExeclError);
        this.autoAdjustColumnWidth(wsError, dataExeclError);
        XLSX.utils.book_append_sheet(wb, wsError, "未识别数据");
      }
      // 导出 Excel 文件
      XLSX.writeFile(wb, `${this.fileName}.xlsx`);
    },
    autoAdjustColumnWidth(ws, data) {
      // 计算列宽
      const colWidths = data[0].map((_, colIndex) =>
        Math.max(
          ...data.map(
            (row) =>
              (row[colIndex] ? row[colIndex].toString().length * 2 : 5) + 5 // 添加一点额外空间
          )
        )
      );
      ws["!cols"] = colWidths.map((width) => ({ wch: width }));
    },
    async exprotSure() {
      this.innerValue = false;
      this.$emit("on-save-success");
    },
  },
};
</script>
