<template>
  <div class="p-5">
    <h3 class="text-center text-xl font-bold mb-5">
      导入《比对文件》Excel文件
    </h3>
    <div class="form-container">
      <MyForm
        ref="form"
        v-model="formData"
        :columns="columnsAddFile"
        label-position="right"
        label-width="220px"
        :formType="'detail'"
        :rules="rules"
      >
        <!-- 自定义的上传文件区域 -->
        <template slot="fileUpload">
          <el-form-item label="">
            <div class="file-upload-container">
              <div class="file-upload-area">
                <el-upload
                  ref="upload"
                  action="#"
                  accept=".xlsx, .xls"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :file-list="fileList"
                  :auto-upload="false"
                  drag
                >
                  <div class="file-icon">
                    <i class="el-icon-upload"></i>
                  </div>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                </el-upload>
                <el-button
                  type="success"
                  @click="importTemplate"
                  style="margin-left: 220px; margin-bottom: 10px"
                  >下载模板</el-button
                >
                <div class="text-sm leading-tight mb-3">
                  导入前需先删除非数据工作表（例如汇总表），仅支持单个数据工作表
                  <br />工作表中不得包含与表头无关的非数据内容，例如标题、数据求和、备注说明等
                  <br />表头需包含:<span class="text-red-500"
                    >*借款申请编号、*借款人、*手机号、*身份证号、*申请开票金额（元）、*资金方、*接收邮箱、*资产方、</span
                  >借款金额（元）、借款日期、结清日期
                </div>
              </div>
            </div>
          </el-form-item>
        </template>
      </MyForm>
    </div>

    <div class="text-center mt-20">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
        >提交</el-button
      >
    </div>
    <UploadToast
      v-model="uploadToastVisible"
      :successList="successList"
      :duplicateList="duplicateList"
      :title="title"
      :valueArr="valueArr"
      :fileName="fileName"
      @on-save-success="handleSaveSuccess"
    ></UploadToast>
  </div>
</template>

<script>
import { newCompanySelectList } from "@/api/businessInformation/companyInformation";
import {
  applicationFileImportDataCheck,
  applicationFileImportData,
} from "@/api/debtConversion/batchInvoicingApplication";
import config from "./config";

export default {
  name: "AddFile",
  components: {},
  data() {
    return {
      ...config,
      // 表单数据
      formData: {
        mainBodyId: undefined,
        invoicingTheme: "",
      },

      fileList: [],
      uploadFile: null, // 保存当前选择的文件
      uploadToastVisible: false,
      successList: [],
      duplicateList: [],
      submitLoading: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getCompanyList();
    },
    // 获取公司列表
    getCompanyList() {
      newCompanySelectList({
        selectCode: "cust",
        modelCode: "DEBTCONVERSION",
      }).then((response) => {
        this.columnsAddFile[0].options = response;
      });
    },
    importTemplate() {
      this.download(this.downloadUrl, {}, `模板_${new Date().getTime()}.xlsx`);
    },
    // 处理文件超出个数限制
    handleExceed() {
      this.$message.error("只能上传1个文件");
    },
    // 文件选择变化时触发
    handleFileChange(file) {
      // 验证文件类型
      const rawFile = file.raw;
      const isExcel =
        rawFile.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        rawFile.type === "application/vnd.ms-excel";

      if (!isExcel) {
        this.$message.error("只能上传Excel文件!");
        this.$refs.upload.clearFiles();
        return;
      }

      this.uploadFile = rawFile;
    },
    // 移除文件时触发
    handleFileRemove() {
      this.uploadFile = null;
    },
    // 表单提交
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (!this.uploadFile) {
            this.$message.error("请上传文件");
            return;
          }
          
          // 创建表单数据对象
          const formData = new FormData();
          formData.append("file", this.uploadFile);

          // 调用上传API
          try {
            this.submitLoading = true;
            const res = await applicationFileImportDataCheck(formData);
            if (res.code === 200) {
              // 成功后跳转回列表页
              this.successList = res.data.successList;
              this.duplicateList = res.data.duplicateList;
              this.handleListExport();
              this.uploadToastVisible = true;
              this.submitLoading = false;
            } else {
              this.submitLoading = false;
              // this.$message.error(res.msg || "导入失败");
            }
          } catch (error) {
            this.submitLoading = false;
          }
        }
      });
    },
    handleListExport() {
      this.successList.forEach((item, index) => {
        item.index = index + 1;
      });
      this.duplicateList.forEach((item, index) => {
        item.index = index + 1;
      });
    },
    async handleSaveSuccess() {
      await applicationFileImportData({
        successList: this.successList,
        ...this.formData,
      });
      this.$message.success("导入成功");
      this.$router.push("/debtConversion/batchInvoicingApplication");
    },
    // 取消操作
    cancel() {
      this.$router.push("/debtConversion/batchInvoicingApplication");
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-upload-list {
  position: absolute;
  top: 300px;
  left: 220px;
}
::v-deep .el-upload {
  margin-left: 220px;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

::v-deep .el-form {
  width: 100%;
}
</style>

