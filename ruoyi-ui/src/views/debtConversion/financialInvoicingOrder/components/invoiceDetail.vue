<template>
  <div class="p-5">
    <div class="mb-2 font-bold text-sm">开票信息：</div>
    <el-descriptions :column="1" border class="custom-descriptions">
      <el-descriptions-item label="开票申请编号">{{
        invoiceInfo.invoicingApplicationCode
      }}</el-descriptions-item>
      <el-descriptions-item label="开票申请时间">{{
        invoiceInfo.invoicingApplicationTime
      }}</el-descriptions-item>
      <el-descriptions-item label="渠道">{{
        invoiceInfo.channel
      }}</el-descriptions-item>
      <el-descriptions-item label="借款人">{{
        invoiceInfo.borrower
      }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{
        invoiceInfo.phoneNum
      }}</el-descriptions-item>
      <el-descriptions-item label="身份证号">{{
        invoiceInfo.idCard
      }}</el-descriptions-item>
      <el-descriptions-item label="开票金额（元）">{{
        invoiceInfo.invoicingAmountTotal
      }}11</el-descriptions-item>
      <el-descriptions-item label="开票主体">{{
        invoiceInfo.mainBodyName
      }}</el-descriptions-item>
      <el-descriptions-item label="接收邮箱">{{
        invoiceInfo.receivingEmail
      }}</el-descriptions-item>

      <el-descriptions-item label="发票附件">
        <MyForm
          v-model="invoiceInfo"
          :columns="invoiceDetailColumns"
          formType="false"
          ref="form"
          @beforeRemove="beforeRemove"
          :customizeRemove="$route.query.type == '2'"
        />
      </el-descriptions-item>

      <el-descriptions-item
        label="原因"
        v-if="$route.query.type == '2'"
        >{{ invoiceInfo.reasonDeleteFile }}</el-descriptions-item
      >
    </el-descriptions>
    <div class="text-center mt-4">
      <el-button @click="$router.back()">返回</el-button>
      <el-button
        :loading="loading"
        @click="submit"
        type="primary"
        :disabled="!Boolean(invoiceInfo.fileIds && invoiceInfo.fileIds.length)"
        >提交</el-button
      >
    </div>
    <Dialog v-model="open" @dialogSubmit="dialogSubmit" />
  </div>
</template>

<script>
import Dialog from "./Dialog.vue";
import { singleinvoice } from "@/api/debtConversion/financialInvoicingOrder";
import { businessDetail } from "@/api/debtConversion/businessInvoicingApplication";
import config from "./config";

import privew from "@/mixin/privew";
export default {
  name: "InvoiceDetail",
  components: { Dialog },

  mixins: [privew],
  data() {
    return {
      ...config,
      loading: false,
      invoiceInfo: {
        invoicingApplicationCode: "",
        invoicingApplicationTime: "",
        channel: "",
        borrower: "",
        phoneNum: "",
        idCard: "",
        invoicingAmountTotal: "",
        mainBodyName: "",
        receivingEmail: "",
        pushTime: "",
        fileIds: [],
        reasonDeleteFile: "",
      },
      open: false,
      pendingRemoveFile: null,
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const { data } = await businessDetail(this.$route.query.id);
      this.invoiceInfo = data;
      if(data.fileUrl!='-'&&data.fileUrl){
        this.invoiceInfo.files=[{fileName:data.fileName,fileUrl:data.fileUrl,id:data.id}]

      }
    },
    beforeRemove(file, fileList) {
      this.pendingRemoveFile = { file, fileList };
      this.open = true;
      return false;
    },
    dialogSubmit(value) {
      this.invoiceInfo.reasonDeleteFile = value;
      // 如果存在待删除的文件，执行删除操作
      if (this.pendingRemoveFile) {
        const { file, fileList } = this.pendingRemoveFile;
        // 从文件列表中移除文件
        const index = fileList.indexOf(file);
        if (index !== -1) {
          fileList.splice(index, 1);
        }
        // 调用 handleRemove 方法删除文件
        this.$refs.form.handleRemove(file, fileList);
        this.pendingRemoveFile = null;
      }
    },
    async submit() {
      this.loading=true;
      const { fileName, fileUrl } = this.invoiceInfo?.fileListAll[0]?.response?.data||{fileName:this.invoiceInfo.fileName,fileUrl:this.invoiceInfo.fileUrl};
      const params = {
        fileName,
        fileUrl,
        id: this.$route.query.id,
        reasonDeleteFile: this.invoiceInfo.reasonDeleteFile,
        receivingEmail:this.invoiceInfo.receivingEmail,
        mainBodyId:this.$route.query.mainBodyId,
      };
      try {
        await singleinvoice(params);
        this.loading=false;
        this.$message.success("提交成功");
        this.$router.back();
      } catch (error) {
        this.loading=false;
      }
     
    },
  },
};
</script>

<style scoped >
::v-deep .el-descriptions-item__label {
  width: 300px;
}
</style>


