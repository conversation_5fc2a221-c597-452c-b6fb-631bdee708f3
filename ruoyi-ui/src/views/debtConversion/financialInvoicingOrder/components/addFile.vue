<template>
  <div class="p-5">
    <h3 class="text-center text-xl font-bold mb-5">导入《发票附件》zip文件</h3>
    <div class="form-container">
      <MyForm
        ref="form"
        v-model="formData"
        :columns="columnsAddFile"
        label-position="right"
        label-width="220px"
        :formType="'detail'"
        :rules="rules"
      >
        <!-- 自定义的上传文件区域 -->
        <template slot="fileUpload">
          <el-form-item label="">
            <div class="file-upload-container">
              <div class="file-upload-area">
                <el-upload
                  ref="upload"
                  action="#"
                  accept=".zip,"
                  :on-change="handleFileChange"
                  :on-remove="handleFileRemove"
                  :file-list="fileList"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :auto-upload="false"
                  drag
                >
                  <div class="file-icon">
                    <i class="el-icon-upload"></i>
                  </div>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                </el-upload>
                <div
                  class="text-sm leading-tight mb-3"
                  style="padding-left: 220px"
                >
                  压缩包单个文件大小不能大于5MB
                </div>
              </div>
            </div>
          </el-form-item>
        </template>
      </MyForm>
    </div>

    <div class="text-center mt-20">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
// import { newCompanySelectList } from "@/api/businessInformation/companyInformation";
import { uploadInvoice } from "@/api/debtConversion/financialInvoicingOrder";

import { getToken } from "@/utils/auth";
import config from "./config";
export default {
  name: "AddFile",
  components: {},
  data() {
    return {
      ...config,
      // 表单数据
      formData: {},
      fileList: [],
      uploadFile: null, // 保存当前选择的文件
      duplicateList: [],
      submitLoading: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      // this.getCompanyList();
      this.formData.mainBodyName = this.$route.query.mainBodyName;
      this.formData.mainBodyId = this.$route.query.mainBodyId;
      this.formData.ids = this.$route.query.selectedRows;
    },
    // 获取公司列表
    getCompanyList() {
      newCompanySelectList({
        selectCode: "cust",
        modelCode: "DEBTCONVERSION",
      }).then((response) => {
        this.columnsAddFile[0].options = response;
      });
    },
    // 处理文件超出个数限制
    handleExceed() {
      this.$message.error("只能上传1个文件");
    },
    // 文件选择变化时触发
    handleFileChange(file) {
      // 验证文件类型
      const rawFile = file.raw;
      const isZip =
        rawFile.type === "application/x-zip-compressed" ||
        rawFile.type === "application/zip" ||
        rawFile.name.toLowerCase().endsWith(".zip");

      if (!isZip) {
        this.$message.error("只能上传ZIP文件!");
        this.$refs.upload.clearFiles();
        return;
      }
      // 验证文件大小（5MB）
      const isLt5M = rawFile.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error("压缩包大小不能超过 5MB!");
        this.$refs.upload.clearFiles();
        return;
      }

      this.uploadFile = rawFile;
    },

    // 移除文件时触发
    handleFileRemove() {
      this.uploadFile = null;
    },
    // 表单提交
    async handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          if (!this.uploadFile) {
            this.$message.error("请上传文件");
            return;
          }
          
          const formData = new FormData();
          formData.append("file", this.uploadFile);
          // Append all properties from this.formData to formData
          Object.keys(this.formData).forEach((key) => {
            formData.append(key, this.formData[key]);
          });

          try {
            this.submitLoading = true;
            await uploadInvoice(formData);
            this.submitLoading = false;
            this.$message.success("导入成功");
            this.$router.push("/debtConversion/financialInvoicingOrder");
          } catch (error) {
            this.submitLoading = false;
          }
        }
      });
    },

    // 取消操作
    cancel() {
      this.$router.push("/debtConversion/financialInvoicingOrder");
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-upload-list {
  position: absolute;
  top: 220px;
  left: 220px;
}
::v-deep .el-upload {
  margin-left: 220px;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

::v-deep .el-form {
  width: 100%;
}
</style>

