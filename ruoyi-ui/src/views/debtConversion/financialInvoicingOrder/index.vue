<template>
  <div class="p-5">
    <MyForm
      v-model="queryParams"
      :columns="formColumns"
      @onSearchList="handleQuery"
      label-width="120px"
    />
    <el-divider></el-divider>
    <div class="flex mb-2 justify-between">
      <div class="font-bold">已选择 {{ selectedRows.length }} 条</div>
    </div>
    <el-tabs
      v-model="queryParams.completionStatus"
      type="card"
      @tab-click="handleQuery"
    >
      <el-tab-pane label="待我处理" name="1"></el-tab-pane>
      <el-tab-pane label="已完成" name="2"></el-tab-pane>
    </el-tabs>
    <div class="flex mb-2 justify-between">
      <div class="flex">
        <el-button
          v-hasPermi="['debtConversion:financialInvoicingOrder:import']"
          v-show="queryParams.completionStatus == '1'"
          :disabled="!Boolean(selectedRows.length)"
          @click="handleImport"
          type="warning"
          plain
          icon="el-icon-upload2"
          size="mini"
          >发票附件上传</el-button
        >
        <el-button
          v-hasPermi="['debtConversion:financialInvoicingOrder:export']"
          @click="handleExport"
          type="primary"
          size="mini"
          plain
          icon="el-icon-plus"
          >批量导出</el-button
        >
      </div>
    </div>
    <MyTable
      ref="table"
      :columns="computedColumns"
      :source="importList"
      showCheckbox
      @selection-change="handleSelectionChange"
    >
      <template #fileName="{ record }">
        <el-tooltip
          class="item"
          effect="dark"
          :content="record.fileName"
          placement="top-start"
        >
          <el-button  v-show="record.fileUrl!='-'" size="mini" type="text" @click="handleDownload(record)">{{
            record.fileName
          }}</el-button>
        </el-tooltip>
      </template>
      <template #operation="{ record }">
        <el-button
          v-hasPermi="['debtConversion:financialInvoicingOrder:paymentImport']"
          v-show="queryParams.completionStatus == '1'"
          size="mini"
          type="text"
          @click="handleView(record)"
          >上传付款附件</el-button
        >
        <el-button
          v-show="queryParams.completionStatus == '2'"
          v-hasPermi="['debtConversion:financialInvoicingOrder:edit']"
          size="mini"
          type="text"
          @click="handleView(record)"
          >编辑</el-button
        >
      </template>
    </MyTable>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { businessList } from "@/api/debtConversion/businessInvoicingApplication";
import config from "./components/config";
import privew from "@/mixin/privew";
import { clone } from "xe-utils";
export default {
  name: "FinancialInvoicingOrder",
  mixins: [privew],
  data() {
    return {
      ...config,
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,

      importList: [],
      // 查询参数
      queryParams: {
        completionStatus: "1",
        pageNum: 1,
        pageSize: 10,
        borrower: undefined,
        idCard: undefined,
        phoneNum: undefined,
        createTime: undefined,
      },

      // 选中的行
      selectedRows: [],
    };
  },
  computed: {
    computedColumns() {
      return this.queryParams.completionStatus === "1"
        ? this.columnsIncomplete
        : this.columnsCompleted;
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    /** 查询导入列表 */
    getList() {
      this.loading = true;
      const params = this.handleParams();
      // 实际应调用API
      businessList(params).then((response) => {
        this.importList = response.rows;
        this.handelList();
        this.total = response.total;
        this.loading = false;
      });
    },
    handelList() {
      this.importList.forEach((item) => {
        item.channelLabel = this.channelObj[item.channel];
      });
    },
    handleParams() {
      const params = clone(this.queryParams, true);
      params.startInvoicingApplicationTime = params?.createTime?.[0];
      params.endInvoicingApplicationTime = params?.createTime?.[1];
      delete params.createTime;
      return params;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.createTime = undefined;
      this.handleQuery();
    },
    handleImport() {
      const firstMainBodyId = this.selectedRows[0].mainBodyId;
      const hasDifferentMainBody = this.selectedRows.some(
        (row) => row.mainBodyId !== firstMainBodyId
      );

      if (hasDifferentMainBody) {
        this.$message.warning("请选择同一开票主体的数据");
        return;
      }

      this.$router.push({
        path: `/debtConversionOther/financialInvoicingOrder/fileImportAdd`,
        query: {
          selectedRows: this.selectedRows.map((item) => item.id),
          mainBodyName: this.selectedRows[0].mainBodyName,
          mainBodyId: this.selectedRows[0].mainBodyId,
        },
      });
    },

    handleExport(record) {
      const params = {
        ...this.handleParams(),
        ids: this.selectedRows?.map((item) => item.id)?.join(","),
        exportTpye: 2,
      };
      this.download("/invoicing/business/export", params, `财务开票工单.xlsx`);
    },

    // 查看详情
    handleView(row) {
      this.$router.push({
        path: `/debtConversionOther/financialInvoicingOrder/invoiceAttachmentDetail/${row.id}${this.queryParams.completionStatus}`,
        query: {
          title: `上传发票附件${row.invoicingApplicationCode}`,
          type: this.queryParams.completionStatus,
          id: row.id,
          mainBodyId: row.mainBodyId,
        },
      });
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
  },
};
</script>

<style scoped>
</style> 