<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi</artifactId>
        <groupId>com.ruoyi</groupId>
        <version>3.8.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ruoyi-quartz</artifactId>

    <description>
        quartz定时任务
    </description>

    <dependencies>

        <!-- 定时任务 -->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-core</artifactId>
        </dependency>


        <dependency>
            <groupId>com.ruoyi</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <dependency>
            <groupId>esigntech-sdk</groupId>
            <artifactId>esigntech-sdk</artifactId>
            <version>2.1.50</version>
        </dependency>
        <dependency>
            <groupId>esigntech-tgtext</groupId>
            <artifactId>esigntech-tgtext</artifactId>
            <version>3.3.64.2150</version>
        </dependency>
        <dependency>
            <groupId>esigntech-sdk-utils</groupId>
            <artifactId>esigntech-sdk-utils</artifactId>
            <version>3.0.6.2150</version>
        </dependency>
        <dependency>
            <groupId>esigntech-sdk-smUtil</groupId>
            <artifactId>esigntech-sdk-utils-smUtil</artifactId>
            <version>1.3.3.2150</version>
        </dependency>
    </dependencies>

</project>
