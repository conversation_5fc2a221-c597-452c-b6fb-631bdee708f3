<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.personnel.mapper.PersonnelArchivesMapper">

    <resultMap type="PersonnelArchivesVo" id="PersonnelArchivesResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="archivesCompany"    column="archives_company"    />
        <result property="archivesCode"    column="archives_code"    />
        <result property="openingBank"    column="opening_bank"    />
        <result property="idCard"    column="id_card"    />
        <result property="birthday"    column="birthday"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="sex"    column="sex"    />
        <result property="hometown"    column="hometown"    />
        <result property="currentAddress"    column="current_address"    />
        <result property="onboardingTime"    column="onboarding_time"    />
        <result property="officeSpace"    column="office_space"    />
        <result property="workTime"    column="work_time"    />
        <result property="personnelType"    column="personnel_type"    />
        <result property="probationMonths"    column="probation_months"    />
        <result property="formalTime"    column="formal_time"    />
        <result property="formalSalary"    column="formal_salary"    />
        <result property="probationSalary"    column="probation_salary"    />
        <result property="directSuperior"    column="direct_superior"    />
        <result property="politicalLandscape"    column="political_landscape"    />
        <result property="sysName"    column="sys_name"    />
        <result property="initialPassword"    column="initial_password"    />
        <result property="salaryAccount"    column="salary_account"    />
        <result property="onboardingRank"    column="onboarding_rank"    />
        <result property="accessCard"    column="access_card"    />
        <result property="officeSupplies"    column="office_supplies"    />
        <result property="fileCabinet"    column="file_cabinet"    />
        <result property="enterpriseEmail"    column="enterprise_email"    />
        <result property="cardPrinting"    column="card_printing"    />
        <result property="attendanceEntry"   column="attendance_entry"/>
        <result property="fileCabinetnum"    column="file_cabinetNum"    />
        <result property="aliAccount"    column="ali_account"    />
        <result property="duty"    column="duty"    />
        <result property="remark"    column="remark"    />
        <result property="dataSources"    column="data_sources"    />
        <result property="personnelState"    column="personnel_state"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="onboardingPostName"    column="onboarding_post_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="deptId" column="dept_id"/>
        <result property="onboardingId" column="onboarding_id"/>
        <result property="leaderName" column="leader_name"/>
        <result property="userId" column="user_id"/>
        <result property="postName" column="post_name"/>
        <result property="localLeader" column="local_leader"/>
        <result property="localLeaderName" column="local_leader_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="companyShortName" column="company_short_name"/>
        <result property="companyCode" column="company_code"/>
    </resultMap>

    <resultMap type="PersonnelArchivesVo" id="PersonnelArchivesTreeResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="userId"    />
        <result property="name"    column="name"    />
        <result property="userName"    column="userName"    />
        <result property="directSuperior"    column="direct_superior"    />
        <collection property="subordinateList" javaType="java.util.List" select="queryPersonnelTreeListById" column="userId"/>
    </resultMap>

    <resultMap id="selectPersonnelIds" type="PersonnelArchivesVo">
        <result property="id"    column="id"    />
        <result property="userId"    column="userId"    />
        <result property="name"    column="name"    />
        <result property="userName"    column="userName"    />
        <result property="userStatus"    column="userStatus"    />
        <result property="directSuperior"    column="directSuperior"    />
        <collection property="subordinateList" select="queryPersonnelTreeListById" javaType="java.util.List" column="userId"/>
    </resultMap>

    <sql id="selectPersonnelArchivesVo">
        select id, name, archives_company,archives_code,opening_bank ,id_card, birthday, phone_num, sex, hometown, current_address, onboarding_time, office_space, work_time, personnel_type, probation_months, formal_time, formal_salary, probation_salary, direct_superior, political_landscape, sys_name, initial_password, salary_account, onboarding_rank, access_card, office_supplies, file_cabinet, enterprise_email, card_printing,attendance_entry, file_cabinetNum, ali_account, duty, remark, data_sources, personnel_state, create_by, create_time, update_by, update_time, ancestors from rs_personnel_archives
    </sql>

    <select id="selectPersonnelArchivesList" parameterType="PersonnelArchivesVo" resultMap="PersonnelArchivesResult">
        select  rpa.id, rpa.name, rpa.archives_company,rpa.archives_code,rpa.opening_bank, rpa.id_card, rpa.birthday, rpa.phone_num, rpa.sex, rpa.hometown, rpa.current_address,
            rpa.onboarding_time, rpa.office_space, rpa.work_time, rpa.personnel_type, rpa.probation_months, rpa.formal_time, rpa.formal_salary, rpa.probation_salary,
            rpa.direct_superior, rpa.political_landscape, rpa.sys_name, rpa.initial_password, rpa.salary_account, rpa.onboarding_rank, rpa.access_card, rpa.office_supplies,
            rpa.file_cabinet, rpa.enterprise_email, rpa.card_printing,rpa.attendance_entry, rpa.file_cabinetNum, rpa.ali_account, rpa.duty, rpa.remark, rpa.data_sources, rpa.personnel_state,
             rpa.create_time, rpa.update_by, rpa.update_time,
            dept.dept_id, dept.dept_name as dept_name,
            leader.nick_name as leader_name,
            user.nick_name as create_by,
            su.user_id as user_id,post.post_name,
            rpa.local_leader,localLeader.nick_name as local_leader_name, rpa.ancestors
        from rs_personnel_archives rpa
        left join sys_user user on user.user_name = rpa.create_by
        left join sys_user su on rpa.sys_name = su.user_name
        left join sys_user_post sup on sup.user_id = su.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join sys_user leader on leader.user_id = rpa.direct_superior
        left join sys_user localLeader on rpa.local_leader = localLeader.user_id
        <where>
            <if test="name != null  and name != ''"> and rpa.name like concat('%', #{name}, '%') </if>
            <if test="archivesCompany != null  and archivesCompany != ''"> and rpa.archives_company = #{archivesCompany}</if>
            <if test="idCard != null  and idCard != ''"> and rpa.id_card like concat('%', #{idCard}, '%')</if>
            <if test="sysName != null  and sysName != ''"> and rpa.sys_name like concat('%', #{sysName}, '%')</if>
            <if test="personnelState != null  and personnelState != ''"> and rpa.personnel_state = #{personnelState}</if>
            <if test="name == null  or name == ''">
                <if test="personnelState == null  or personnelState == ''"> and rpa.personnel_state != '3'</if>
            </if>
            <if test="deptId != null  and deptId != ''"> and dept.dept_id = #{deptId}</if>
            <if test="postId != null  and postId != ''"> and sup.post_id  = #{postId}</if>
            <if test="onboardingTimeStart != null  and onboardingTimeStart != ''"> and rpa.onboarding_time >= #{onboardingTimeStart}</if>
            <if test="onboardingTimeEnd != null  and onboardingTimeEnd != ''"> and #{onboardingTimeEnd} >= rpa.onboarding_time</if>
            <if test="idArray != null and idArray.size() > 0">
                and rpa.id in
                <foreach collection="idArray" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or rpa.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                    or rpa.archives_company in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </trim>
        </where>
        group by id
        order by rpa.id desc
    </select>

    <select id="selectPersonnelArchivesById" parameterType="Long" resultMap="PersonnelArchivesResult">
        <include refid="selectPersonnelArchivesVo"/>
        where id = #{id}
    </select>

    <insert id="insertPersonnelArchives" parameterType="PersonnelArchives" useGeneratedKeys="true" keyProperty="id">
        insert into rs_personnel_archives
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="archivesCompany != null">archives_company,</if>
            <if test="archivesCode != null">archives_code,</if>
            <if test="openingBank != null and openingBank != ''">opening_bank,</if>
            <if test="idCard != null and idCard != ''">id_card,</if>
            <if test="birthday != null">birthday,</if>
            <if test="phoneNum != null and phoneNum != ''">phone_num,</if>
            <if test="sex != null and sex != ''">sex,</if>
            <if test="hometown != null">hometown,</if>
            <if test="currentAddress != null">current_address,</if>
            <if test="onboardingTime != null">onboarding_time,</if>
            <if test="officeSpace != null">office_space,</if>
            <if test="workTime != null">work_time,</if>
            <if test="personnelType != null">personnel_type,</if>
            <if test="probationMonths != null">probation_months,</if>
            <if test="formalTime != null">formal_time,</if>
            <if test="formalSalary != null">formal_salary,</if>
            <if test="probationSalary != null">probation_salary,</if>
            <if test="directSuperior != null">direct_superior,</if>
            <if test="politicalLandscape != null">political_landscape,</if>
            <if test="sysName != null and sysName != ''">sys_name,</if>
            <if test="initialPassword != null and initialPassword != ''">initial_password,</if>
            <if test="salaryAccount != null and salaryAccount != ''">salary_account,</if>
            <if test="onboardingRank != null">onboarding_rank,</if>
            <if test="accessCard != null">access_card,</if>
            <if test="officeSupplies != null">office_supplies,</if>
            <if test="fileCabinet != null">file_cabinet,</if>
            <if test="enterpriseEmail != null and enterpriseEmail != ''">enterprise_email,</if>
            <if test="cardPrinting != null">card_printing,</if>
            <if test="attendanceEntry != null">attendance_entry,</if>
            <if test="fileCabinetnum != null">file_cabinetNum,</if>
            <if test="aliAccount != null">ali_account,</if>
            <if test="duty != null">duty,</if>
            <if test="remark != null">remark,</if>
            <if test="dataSources != null and dataSources != ''">data_sources,</if>
            <if test="personnelState != null and personnelState != ''">personnel_state,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="archivesCompany != null">#{archivesCompany},</if>
            <if test="archivesCode != null">#{archivesCode},</if>
            <if test="openingBank != null and openingBank != ''">#{openingBank},</if>
            <if test="idCard != null and idCard != ''">#{idCard},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="phoneNum != null and phoneNum != ''">#{phoneNum},</if>
            <if test="sex != null and sex != ''">#{sex},</if>
            <if test="hometown != null">#{hometown},</if>
            <if test="currentAddress != null">#{currentAddress},</if>
            <if test="onboardingTime != null">#{onboardingTime},</if>
            <if test="officeSpace != null">#{officeSpace},</if>
            <if test="workTime != null">#{workTime},</if>
            <if test="personnelType != null">#{personnelType},</if>
            <if test="probationMonths != null">#{probationMonths},</if>
            <if test="formalTime != null">#{formalTime},</if>
            <if test="formalSalary != null">#{formalSalary},</if>
            <if test="probationSalary != null">#{probationSalary},</if>
            <if test="directSuperior != null">#{directSuperior},</if>
            <if test="politicalLandscape != null">#{politicalLandscape},</if>
            <if test="sysName != null and sysName != ''">#{sysName},</if>
            <if test="initialPassword != null and initialPassword != ''">#{initialPassword},</if>
            <if test="salaryAccount != null and salaryAccount != ''">#{salaryAccount},</if>
            <if test="onboardingRank != null">#{onboardingRank},</if>
            <if test="accessCard != null">#{accessCard},</if>
            <if test="officeSupplies != null">#{officeSupplies},</if>
            <if test="fileCabinet != null">#{fileCabinet},</if>
            <if test="enterpriseEmail != null and enterpriseEmail != ''">#{enterpriseEmail},</if>
            <if test="cardPrinting != null">#{cardPrinting},</if>
            <if test="attendanceEntry != null">#{attendanceEntry},</if>
            <if test="fileCabinetnum != null">#{fileCabinetnum},</if>
            <if test="aliAccount != null">#{aliAccount},</if>
            <if test="duty != null">#{duty},</if>
            <if test="remark != null">#{remark},</if>
            <if test="dataSources != null and dataSources != ''">#{dataSources},</if>
            <if test="personnelState != null and personnelState != ''">#{personnelState},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        </trim>
    </insert>

    <update id="updatePersonnelArchives" parameterType="PersonnelArchives">
        update rs_personnel_archives
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="archivesCompany != null">archives_company = #{archivesCompany},</if>
            <if test="archivesCode != null">archives_code = #{archivesCode},</if>
            <if test="openingBank != null and openingBank != ''">opening_bank = #{openingBank},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="phoneNum != null and phoneNum != ''">phone_num = #{phoneNum},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="hometown != null">hometown = #{hometown},</if>
            <if test="currentAddress != null">current_address = #{currentAddress},</if>
            <if test="onboardingTime != null">onboarding_time = #{onboardingTime},</if>
            <if test="officeSpace != null">office_space = #{officeSpace},</if>
            <if test="workTime != null">work_time = #{workTime},</if>
            <if test="personnelType != null">personnel_type = #{personnelType},</if>
            <if test="probationMonths != null">probation_months = #{probationMonths},</if>
            <if test="formalTime != null">formal_time = #{formalTime},</if>
            <if test="formalSalary != null">formal_salary = #{formalSalary},</if>
            <if test="probationSalary != null">probation_salary = #{probationSalary},</if>
            <if test="directSuperior != null">direct_superior = #{directSuperior},</if>
            <if test="politicalLandscape != null">political_landscape = #{politicalLandscape},</if>
            <if test="sysName != null and sysName != ''">sys_name = #{sysName},</if>
            <if test="initialPassword != null and initialPassword != ''">initial_password = #{initialPassword},</if>
            <if test="salaryAccount != null and salaryAccount != ''">salary_account = #{salaryAccount},</if>
            <if test="onboardingRank != null">onboarding_rank = #{onboardingRank},</if>
            <if test="accessCard != null">access_card = #{accessCard},</if>
            <if test="officeSupplies != null">office_supplies = #{officeSupplies},</if>
            <if test="fileCabinet != null">file_cabinet = #{fileCabinet},</if>
            <if test="enterpriseEmail != null and enterpriseEmail != ''">enterprise_email = #{enterpriseEmail},</if>
            <if test="cardPrinting != null">card_printing = #{cardPrinting},</if>
            <if test="attendanceEntry != null">attendance_entry = #{attendanceEntry},</if>
            <if test="fileCabinetnum != null">file_cabinetNum = #{fileCabinetnum},</if>
            <if test="aliAccount != null">ali_account = #{aliAccount},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dataSources != null and dataSources != ''">data_sources = #{dataSources},</if>
            <if test="personnelState != null and personnelState != ''">personnel_state = #{personnelState},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatePersonnelArchivesForInit" parameterType="PersonnelArchives">
        update rs_personnel_archives
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="archivesCompany != null">archives_company = #{archivesCompany},</if>
            <if test="archivesCode != null">archives_code = #{archivesCode},</if>
            <if test="openingBank != null and openingBank != ''">opening_bank = #{openingBank},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="phoneNum != null and phoneNum != ''">phone_num = #{phoneNum},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="hometown != null">hometown = #{hometown},</if>
            <if test="currentAddress != null">current_address = #{currentAddress},</if>
            <if test="onboardingTime != null">onboarding_time = #{onboardingTime},</if>
            <if test="officeSpace != null">office_space = #{officeSpace},</if>
            <if test="workTime != null">work_time = #{workTime},</if>
            <if test="personnelType != null">personnel_type = #{personnelType},</if>
            <if test="probationMonths != null">probation_months = #{probationMonths},</if>
            <if test="formalTime != null">formal_time = #{formalTime},</if>
            <if test="formalSalary != null">formal_salary = #{formalSalary},</if>
            <if test="probationSalary != null">probation_salary = #{probationSalary},</if>
            <if test="directSuperior != null">direct_superior = #{directSuperior},</if>
            <if test="politicalLandscape != null">political_landscape = #{politicalLandscape},</if>
            <if test="sysName != null and sysName != ''">sys_name = #{sysName},</if>
            <if test="initialPassword != null and initialPassword != ''">initial_password = #{initialPassword},</if>
            <if test="salaryAccount != null and salaryAccount != ''">salary_account = #{salaryAccount},</if>
            <if test="onboardingRank != null">onboarding_rank = #{onboardingRank},</if>
            <if test="accessCard != null">access_card = #{accessCard},</if>
            <if test="officeSupplies != null">office_supplies = #{officeSupplies},</if>
            <if test="fileCabinet != null">file_cabinet = #{fileCabinet},</if>
            <if test="enterpriseEmail != null and enterpriseEmail != ''">enterprise_email = #{enterpriseEmail},</if>
            <if test="cardPrinting != null">card_printing = #{cardPrinting},</if>
            <if test="attendanceEntry != null">attendance_entry = #{attendanceEntry},</if>
            <if test="fileCabinetnum != null">file_cabinetNum = #{fileCabinetnum},</if>
            <if test="aliAccount != null">ali_account = #{aliAccount},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="dataSources != null and dataSources != ''">data_sources = #{dataSources},</if>
            <if test="personnelState != null and personnelState != ''">personnel_state = #{personnelState},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePersonnelArchivesById" parameterType="Long">
        delete from rs_personnel_archives where id = #{id}
    </delete>

    <delete id="deletePersonnelArchivesByIds" parameterType="String">
        delete from rs_personnel_archives where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO rs_personnel_archives (
        name,archives_company,archives_code,opening_bank,id_card,birthday,
        phone_num,sex,hometown,current_address,onboarding_time,office_space,
        work_time,personnel_type,probation_months,formal_time,formal_salary,
        probation_salary,direct_superior,political_landscape,sys_name,initial_password,
        salary_account,onboarding_rank,access_card,office_supplies,file_cabinet,
        enterprise_email,card_printing,attendance_entry,file_cabinetNum,ali_account,duty,remark,
        data_sources,personnel_state,create_by,create_time,update_by,update_time,local_leader,ancestors
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.name},#{item.archivesCompany},#{item.archivesCode},#{item.openingBank},#{item.idCard},#{item.birthday},
            #{item.phoneNum},#{item.sex},#{item.hometown},#{item.currentAddress},#{item.onboardingTime},#{item.officeSpace},
            #{item.workTime},#{item.personnelType},#{item.probationMonths},#{item.formalTime},#{item.formalSalary},
            #{item.probationSalary},#{item.directSuperior},#{item.politicalLandscape},#{item.sysName},#{item.initialPassword},
            #{item.salaryAccount},#{item.onboardingRank},#{item.accessCard},#{item.officeSupplies},#{item.fileCabinet},
            #{item.enterpriseEmail},#{item.cardPrinting},#{item.attendanceEntry},#{item.fileCabinetnum},#{item.aliAccount},#{item.duty},#{item.remark},
            #{item.dataSources},#{item.personnelState},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime},#{item.localLeader},#{item.ancestors})
        </foreach>
    </insert>

    <insert id="batchInsertExcel" parameterType="java.util.List">
        INSERT INTO rs_personnel_archives (
        name,archives_company,archives_code,opening_bank,id_card,birthday,
        phone_num,sex,hometown,current_address,onboarding_time,office_space,
        work_time,personnel_type,probation_months,formal_time,formal_salary,
        probation_salary,direct_superior,political_landscape,sys_name,initial_password,
        salary_account,onboarding_rank,access_card,office_supplies,file_cabinet,
        enterprise_email,card_printing,attendance_entry,file_cabinetNum,ali_account,duty,remark,
        data_sources,personnel_state,create_by,create_time,update_by,update_time, ancestors
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.name},#{item.archivesCompany},#{item.archivesCode},#{item.openingBank},#{item.idCard},#{item.birthday},
            #{item.phoneNum},#{item.sex},#{item.hometown},#{item.currentAddress},#{item.onboardingTime},#{item.officeSpace},
            #{item.workTime},#{item.personnelType},#{item.probationMonths},#{item.formalTime},#{item.formalSalary},
            #{item.probationSalary},#{item.directSuperior},#{item.politicalLandscape},#{item.sysName},#{item.initialPassword},
            #{item.salaryAccount},#{item.onboardingRank},#{item.accessCard},#{item.officeSupplies},#{item.fileCabinet},
            #{item.enterpriseEmail},#{item.cardPrinting},#{item.attendanceEntry},#{item.fileCabinetnum},#{item.aliAccount},#{item.duty},#{item.remark},
            #{item.dataSources},#{item.personnelState},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime}, #{item.ancestors})
        </foreach>
    </insert>

    <select id="selectListByIdCard" resultType="org.ruoyi.core.personnel.domain.PersonnelArchives">
        <include refid="selectPersonnelArchivesVo"/>
        where id_card in
        <foreach item="idCard" collection="array" open="(" separator="," close=")">
            #{idCard}
        </foreach>
    </select>

    <select id="selectListBySysName" resultType="org.ruoyi.core.personnel.domain.PersonnelArchives">
        <include refid="selectPersonnelArchivesVo"/>
        where sys_name in
        <foreach item="sysName" collection="array" open="(" separator="," close=")">
            #{sysName}
        </foreach>
    </select>

    <select id="getCountByIdCard" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM rs_personnel_archives
        where id_card = #{idCard}
        and personnel_state in (0,1,2)
    </select>

    <select id="getCountByCreateTime" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM rs_personnel_archives
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <select id="selectPersonnelArchivesListForTransfer" parameterType="PersonnelArchivesVo" resultType="PersonnelArchivesVo">
        select rpa.id, rpa.archives_company,rpa.name, rpa.archives_code,rpa.opening_bank ,rpa.id_card, rpa.birthday, rpa.phone_num, rpa.sex,
        rpa.hometown, rpa.current_address,rpa.onboarding_time, rpa.office_space, rpa.work_time, rpa.personnel_type, rpa.probation_months,
        rpa.formal_time, rpa.formal_salary, rpa.probation_salary,rpa.direct_superior, rpa.political_landscape,
        rpa.sys_name, rpa.initial_password, rpa.salary_account, rpa.onboarding_rank, rpa.access_card, rpa.office_supplies,
        rpa.file_cabinet, rpa.enterprise_email, rpa.card_printing,rpa.attendance_entry, rpa.file_cabinetNum, rpa.ali_account,
        rpa.duty, rpa.data_sources, rpa.personnel_state,
        rpa.create_time, rpa.update_by, rpa.update_time,
        rpp.process_id ,rpp.process_state,
        rpt.id as transfer_id, rpt.out_dept, rpt.in_dept, rpt.out_post, rpt.in_post, rpt.out_leader, rpt.in_leader, rpt.effectiveDate,
        rpt.is_salary,rpt.salary_type ,rpt.old_salary, rpt.new_salary, rpt.reason, rpt.remark,
        leader.nick_name as leader_name,
        dept.dept_id, dept.dept_name as dept_name,
        xp.id as payrollfile_id,xpr.new_record_money as payrollfile_money,
        user.nick_name as create_by,
        sc.company_short_name
        from rs_personnel_archives rpa
        left join sys_user user on user.user_name = rpa.create_by
        left join rs_personnel_transfer rpt on rpt.archives_id = rpa.id and rpt.is_execute = '1'
        left join rs_personnel_process rpp on rpt.id = rpp.correlation_id and rpp.process_type = '3' and rpp.process_state IN (1,3)
        left join sys_user leader on rpa.direct_superior = leader.user_id
        left join sys_user su on rpa.sys_name = su.user_name
        left join sys_user_post sup on sup.user_id = su.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join xzda_payrollfile xp on rpa.id_card = xp.id_card
        left join xzda_payrollfile_record xpr on xpr.id = xp.new_record_id
        left join sys_company sc on rpa.archives_company = sc.id
        <where>
            rpa.personnel_state in (0,1)
            <if test="name != null  and name != ''"> and rpa.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and rpa.id_card like concat('%', #{idCard}, '%')</if>
            <if test="sysName != null  and sysName != ''"> and rpa.sys_name like concat('%', #{sysName}, '%')</if>
            <if test="personnelState != null  and personnelState != ''"> and rpa.personnel_state = #{personnelState}</if>
            <if test="deptId != null and deptId != ''"> and dept.dept_id = #{deptId}</if>
            <if test="postId != null and postId != ''"> and sup.post_id  = #{postId}</if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="createBy != null and createBy != ''">
                    or rpa.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </trim>
        </where>
        group by id,process_id
        order by rpa.create_time desc
    </select>

    <select id="getFormalBeforeList" parameterType="PersonnelArchivesVo" resultMap="PersonnelArchivesResult">
        SELECT rpa.id, name, archives_code,opening_bank, id_card, birthday, phone_num, rpa.sex, hometown, current_address,
        onboarding_time, office_space, work_time, personnel_type, rpa.probation_months, rpa.formal_time, rpa.formal_salary, rpa.probation_salary,
        rpa.direct_superior, rpa.political_landscape, sys_name, initial_password, salary_account, onboarding_rank, access_card, office_supplies,
        file_cabinet, enterprise_email, card_printing,attendance_entry, file_cabinetNum, ali_account, rpa.duty, rpa.remark, data_sources, personnel_state,
        rpa.create_time, rpa.update_by, rpa.update_time,
        user.nick_name as create_by
        from rs_personnel_archives rpa
        left join sys_user user on user.user_name = rpa.create_by
        LEFT JOIN rs_personnel_formal rpf ON rpa.id = rpf.onboarding_id
        <where>
            rpf.archives_id IS NULL
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card like concat('%', #{idCard}, '%')</if>
            <if test="sysName != null  and sysName != ''"> and sys_name like concat('%', #{sysName}, '%')</if>
            <if test="personnelState != null  and personnelState != ''"> and personnel_state = #{personnelState}</if>
            <if test="onboardingTimeStart != null  and onboardingTimeStart != ''"> and onboarding_time >= #{onboardingTimeStart}</if>
            <if test="onboardingTimeEnd != null  and onboardingTimeEnd != ''"> and #{onboardingTimeEnd} >= onboarding_time</if>
            <if test="idArray != null and idArray.size() > 0">
                AND rpa.id IN
                <foreach collection="idArray" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


    <select id="listForResignation" parameterType="PersonnelArchivesVo" resultType="PersonnelArchivesVo">
        select  rpa.id, rpa.name, rpa.archives_code, rpa.id_card, rpa.birthday, rpa.phone_num, rpa.sex, rpa.hometown, rpa.current_address,
        rpa.onboarding_time, rpa.office_space, rpa.work_time, rpa.personnel_type, rpa.probation_months, rpa.formal_time, rpa.formal_salary, rpa.probation_salary,
        rpa.direct_superior, rpa.political_landscape, rpa.sys_name, rpa.initial_password, rpa.salary_account, rpa.onboarding_rank, rpa.access_card, rpa.office_supplies,
        rpa.file_cabinet, rpa.enterprise_email, rpa.card_printing,rpa.attendance_entry, rpa.file_cabinetNum, rpa.ali_account, rpa.duty, rpa.remark, rpa.data_sources, rpa.personnel_state,
        rpa.create_time, rpa.update_by, rpa.update_time,
        dept.dept_id,
        dept.unit_id,dept.dept_name as deptName,
        user.nick_name as create_by,
        leader.nick_name as leaderName
        from rs_personnel_archives rpa
        left join sys_user leader on leader.user_id = rpa.direct_superior
        left join sys_user user on user.user_name = rpa.create_by
        left join sys_user su on rpa.sys_name = su.user_name
        left join sys_user_post sup on sup.user_id = su.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        <where >
            rpa.personnel_state in (0,1)
        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                <if test="sysName != null and sysName != ''">
                    or rpa.sys_name = #{sysName}
                </if>
                <if test="createBy != null and createBy != ''">
                    or rpa.create_by = #{createBy}
                </if>
                <if test="unitIds != null and unitIds.size() > 0">
                    or dept.unit_id in
                    <foreach item="unitId" collection="unitIds" open="(" close=")" separator=",">
                        #{unitId}
                    </foreach>
                </if>
                <if test="deptIds != null and deptIds.size() > 0">
                    or dept.dept_id in
                    <foreach item="deptId" collection="deptIds" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </trim>
        </where>
        group by id
        order by rpa.create_time desc
    </select>

    <update id="updatePersonnelStateBySysName" parameterType="PersonnelArchives">
        update rs_personnel_archives
        <trim prefix="SET" suffixOverrides=",">
            <if test="personnelState != null and personnelState != ''">personnel_state = #{personnelState},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where sys_name = #{sysName}
    </update>

    <select id="selectListOfMonthLog" resultMap="PersonnelArchivesResult">
        select  rpa.name, rpa.archives_company, rpa.id_card,rpa.direct_superior,rpa.sys_name,user.user_id as id,rpa.local_leader
        from rs_personnel_archives rpa
        left join sys_user user on rpa.sys_name = user.user_name
        where personnel_state != '3'
    </select>

    <select id="getSubordinateList" resultMap="PersonnelArchivesResult" parameterType="String">
        select  rpa1.name,rpa1.sys_name
        from rs_personnel_archives rpa1
        where direct_superior in
            (
            select  user.user_id
            from rs_personnel_archives rpa
            left join sys_user user on rpa.sys_name = user.user_name
            where rpa.sys_name = #{sysName}
            )
    </select>

    <select id="subordinateList" parameterType="Long" resultType="PersonnelArchivesVo">
        select
            rpa.name, user.user_id ,user.status, rpa.direct_superior, syp.post_id, post.dept_id, post.post_name
        from rs_personnel_archives rpa
                 left join  sys_user user on rpa.sys_name = user.user_name
                 left join  sys_user_post syp on syp.user_id = user.user_id
                 left join  sys_post post on post.post_id = syp.post_id
        where syp.home_post = 0 and user.status = '0'
        and (rpa.direct_superior = #{userId} or user.user_id = #{userId})
    </select>

    <select id="queryParentPersonnelBySysName" resultMap="selectPersonnelIds">
        select rpa.id as 'id', su.user_id as 'userId', su.user_name as 'userName', su.nick_name as 'name', rpa.direct_superior as 'directSuperior', su.status as 'userStatus'
        from rs_personnel_archives rpa
                 left join sys_user su on rpa.sys_name = su.user_name
                 where rpa.sys_name = #{userName}
    </select>

    <select id="queryPersonnelTreeListById" resultMap="selectPersonnelIds">
        select rpa.id as 'id', su.user_id as 'userId', su.user_name as 'userName', su.nick_name as 'name', rpa.direct_superior as 'directSuperior', su.status as 'userStatus'
        from rs_personnel_archives rpa
                 left join sys_user su on rpa.sys_name = su.user_name
        where rpa.direct_superior = #{id}
    </select>

    <select id="queryPersonnelOrganization" resultMap="PersonnelArchivesTreeResult">
        select user_id as 'userId', user_name as 'userName', nick_name as 'name' from sys_user where user_name = #{userName}
    </select>

    <select id="selectPerHomePostById" resultType="com.ruoyi.system.domain.vo.SysPostVo">
        select sp.*,su.user_id as 'userId' from sys_post sp
               left join sys_user_post sup on sp.post_id = sup.post_id
               left join sys_user su on sup.user_id = su.user_id
               left join rs_personnel_archives rpa on su.user_name = rpa.sys_name
        where rpa.sys_name = (select sys_name from rs_personnel_archives where id = #{id}) and sup.home_post = 0
    </select>

    <select id="selectPersonnelArchivesByName" parameterType="string" resultType="org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo">
        select rpa.id as 'id', su.user_id as 'userId', su.user_name as 'userName', su.nick_name as 'name' from rs_personnel_archives rpa
            left join sys_user su on rpa.direct_superior = su.user_id
            where rpa.sys_name = #{userName}

    </select>

    <select id="selectPersonnelArchivesInfoBysysName" parameterType="String" resultMap="PersonnelArchivesResult">
        select  rpa.id, rpa.name, rpa.archives_company,rpa.archives_code,rpa.opening_bank, rpa.id_card, rpa.birthday, rpa.phone_num, rpa.sex, rpa.hometown, rpa.current_address,
        rpa.onboarding_time, rpa.office_space, rpa.work_time, rpa.personnel_type, rpa.probation_months, rpa.formal_time, rpa.formal_salary, rpa.probation_salary,
        rpa.direct_superior, rpa.political_landscape, rpa.sys_name, rpa.initial_password, rpa.salary_account, rpa.onboarding_rank, rpa.access_card, rpa.office_supplies,
        rpa.file_cabinet, rpa.enterprise_email, rpa.card_printing,rpa.attendance_entry, rpa.file_cabinetNum, rpa.ali_account, rpa.duty, rpa.remark, rpa.data_sources, rpa.personnel_state,
        rpa.create_time, rpa.update_by, rpa.update_time,
        dept.dept_id, dept.dept_name as dept_name,
        user.nick_name as create_by,
        su.user_id as user_id,post.post_name ,
        sc.company_short_name,sc.company_code
        from rs_personnel_archives rpa
        left join sys_user user on user.user_name = rpa.create_by
        left join sys_user su on rpa.sys_name = su.user_name
        left join sys_user_post sup on sup.user_id = su.user_id and sup.home_post = 0
        left join sys_post post on sup.post_id = post.post_id
        left join sys_dept dept on dept.dept_id = post.dept_id
        left join sys_company sc on rpa.archives_company = sc.id
        where rpa.sys_name = #{sysName}
        group by id
        order by rpa.id desc
    </select>
</mapper>

