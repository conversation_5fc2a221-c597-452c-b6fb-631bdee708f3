<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.superviseInformation.mapper.SuperviseInformationCatalogueMapper">

    <resultMap type="org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO" id="InformationCatalogueResult">
        <result property="id"    column="id"    />
        <result property="catalogueName"    column="catalogue_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="catalogueSystemCode"    column="catalogue_system_code"    />
        <result property="catalogueCode"    column="catalogue_code"    />
        <result property="cooperationCompany"    column="cooperation_company"    />
        <result property="cooperationProject"    column="cooperation_project"    />
        <result property="catalogueType"    column="catalogue_type"    />
        <result property="isPublic"    column="is_public"    />
        <result property="orgId"    column="org_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="remake"    column="remake"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="cooperationCompanyName"    column="cooperation_company_name"    />
        <result property="cooperationProjectName"    column="cooperation_project_name"    />
    </resultMap>


    <resultMap type="org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO" id="InformationCatalogueVOResult">
        <result property="id"    column="id"    />
        <result property="catalogueName"    column="catalogue_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="catalogueSystemCode"    column="catalogue_system_code"    />
        <result property="catalogueCode"    column="catalogue_code"    />
        <result property="orgId"    column="org_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="remake"    column="remake"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="orgName"    column="org_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="parentName" column="parent_name" />
        <result property="isPublic" column="is_public" />
        <result property="unitId" column="unit_id" />
        <result property="parentOrgId" column="parent_org_id" />
    </resultMap>

    <sql id="selectInformationCatalogueVo">
        select id, catalogue_name, parent_id, catalogue_system_code, catalogue_code,cooperation_company, cooperation_project, catalogue_type, is_public, org_id, dept_id, order_num, remake, create_by, create_time, update_by, update_time from zl_supervise_information_catalogue
    </sql>

    <select id="selectInformationCatalogueList" parameterType="org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO" resultMap="InformationCatalogueVOResult">
        select ic.id, ic.catalogue_name, ic.parent_id, ic.catalogue_system_code, ic.catalogue_code,
               ic.cooperation_company, ic.cooperation_project, ic.catalogue_type, ic.is_public,
               sc.company_name as cooperation_company_name,opd.project_name as cooperation_project_name,
               ic.org_id, ic.dept_id, ic.order_num,
               ic.remake, ic.create_time, ic.update_by, ic.update_time,
                dept.dept_name as dept_name,
                org.dept_name as org_name,org.unit_id,
                user.nick_name as create_by,ziic.org_id as parent_org_id,ziic.catalogue_name as parent_name
        from zl_supervise_information_catalogue ic
        left join sys_user user on user.user_name = ic.create_by
        LEFT join sys_dept dept on ic.dept_id = dept.dept_id
        LEFT join sys_dept org on ic.org_id = org.dept_id
        left join zl_supervise_information_catalogue ziic on ziic.id = ic.parent_id
        left join sys_company sc on ic.cooperation_company = sc.id
        left join oa_project_deploy opd on ic.cooperation_project = opd.id
        <where>
            <if test="catalogueName != null  and catalogueName != ''"> and ic.catalogue_name like concat('%', #{catalogueName}, '%')</if>
            <if test="catalogueSystemCode != null  and catalogueSystemCode != ''"> and ic.catalogue_system_code = #{catalogueSystemCode}</if>
            <if test="catalogueCode != null  and catalogueCode != ''"> and ic.catalogue_code like concat('%', #{catalogueCode}, '%')</if>
            <if test="cooperationCompanyName != null  and cooperationCompanyName != ''"> and sc.company_name like concat('%', #{cooperationCompanyName}, '%')</if>
            <if test="cooperationProjectName != null  and cooperationProjectName != ''"> and opd.project_name like concat('%', #{cooperationProjectName}, '%')</if>
            <if test="unitId != null "> and org.unit_id = #{unitId}</if>
            <if test="deptId != null "> and ic.dept_id = #{deptId}</if>
            <if test="isPublic != null "> and ic.is_public = #{isPublic}</if>
            <if test="orderNum != null "> and ic.order_num = #{orderNum}</if>
            <if test="createBy != null "> and ic.create_by = #{createBy}</if>
            <if test="orgIds != null and orgIds.size() > 0">
                and ic.org_id in
                <foreach collection="orgIds" item="OrgId" separator="," open="(" close=")">
                    #{OrgId}
                </foreach>
            </if>
            <if test="ids != null and ids.size() > 0">
                and ic.id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and|or">
                or ic.org_id = 0
                 <if test="auCompanyIds != null and auCompanyIds.size() > 0">
                     or org.unit_id in
                     <foreach collection="auCompanyIds" item="auCompanyId" separator="," open="(" close=")">
                         #{auCompanyId}
                     </foreach>
                 </if>
                 <if test="auProjectIds != null and auProjectIds.size() > 0">
                     or ic.cooperation_project in
                     <foreach collection="auProjectIds" item="auProjectId" separator="," open="(" close=")">
                         #{auProjectId}
                     </foreach>
                 </if>
                <if test="auIds != null and auIds.size() > 0">
                    or ic.id in
                    <foreach collection="auIds" item="auId" separator="," open="(" close=")">
                        #{auId}
                    </foreach>
                </if>
            </trim>
            <if test="informationRetrieval != null  and informationRetrieval != ''">
                and (
                    ic.catalogue_code like concat('%', #{informationRetrieval}, '%')
                    or ic.catalogue_name like concat('%', #{informationRetrieval}, '%')
                    or ic.catalogue_system_code like concat('%', #{informationRetrieval}, '%')
                    or dept.dept_name like concat('%', #{informationRetrieval}, '%')
                    or org.dept_name like concat('%', #{informationRetrieval}, '%')
                    or sc.company_name like concat('%', #{informationRetrieval}, '%')
                    or opd.project_name like concat('%', #{informationRetrieval}, '%')
                    )
            </if>
        </where>
            group by ic.id
            ORDER BY ic.order_num ASC,ic.create_time DESC
    </select>

    <select id="selectCatalogueListOfAuthority" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue" resultMap="InformationCatalogueVOResult">
        select ic.id, ic.catalogue_name, ic.parent_id, ic.catalogue_system_code, ic.catalogue_code,
        ic.cooperation_company, ic.cooperation_project, ic.catalogue_type, ic.is_public,
        ic.org_id, ic.dept_id, ic.order_num,
        ic.remake, ic.create_time, ic.update_by, ic.update_time,
        dept.dept_name as dept_name,
        org.dept_name as org_name,org.unit_id,
        user.nick_name as create_by,ziic.org_id as parent_org_id
        from zl_supervise_information_catalogue ic
        left join sys_user user on user.user_name = ic.create_by
        LEFT join sys_dept dept on ic.dept_id = dept.dept_id
        LEFT join sys_dept org on ic.org_id = org.dept_id
#         left join zl_authority au ON ic.id = au.bill_id and au.bill_type = 0
        left join zl_supervise_information_catalogue ziic on ziic.id = ic.parent_id
        <where>
            and ic.parent_id = 0
            and ( ic.create_by = #{createBy}
<!--            <if test="auDeptIds != null and auDeptIds.size() > 0">-->
<!--                OR (au.authority_id IN-->
<!--                <foreach collection="auDeptIds" item="auDeptId" separator="," open="(" close=")">-->
<!--                    #{auDeptId}-->
<!--                </foreach>-->
<!--                AND au.authority_type = '0' and au.del_flag = 0)-->
<!--            </if>-->
<!--            OR (au.authority_id = #{auUserId} AND au.authority_type = '2' and au.del_flag = 0)-->
<!--            <if test="auPostIds != null and auPostIds.size() > 0">-->
<!--                OR (au.authority_id IN-->
<!--                <foreach collection="auPostIds" item="auPostId" separator="," open="(" close=")">-->
<!--                    #{auPostId}-->
<!--                </foreach>-->
<!--                AND au.authority_type = '1' and au.del_flag = 0)-->
<!--            </if>-->
            )
            <if test="catalogueName != null  and catalogueName != ''"> and ic.catalogue_name like concat('%', #{catalogueName}, '%')</if>
            <if test="catalogueSystemCode != null  and catalogueSystemCode != ''"> and ca.catalogue_system_code = #{catalogueSystemCode}</if>
            <if test="catalogueCode != null  and catalogueCode != ''"> and ic.catalogue_code like concat('%', #{catalogueCode}, '%')</if>
            <if test="unitId != null "> and org.unit_id = #{unitId}</if>
            <if test="deptId != null "> and ic.dept_id = #{deptId}</if>
            <if test="orderNum != null "> and ic.order_num = #{orderNum}</if>
            <if test="remake != null  and remake != ''"> and remake = #{remake} </if>
        </where>
        group by ic.id
        ORDER BY ic.order_num ASC,ic.create_time DESC
    </select>

    <select id="selectInformationCatalogueById" parameterType="Long" resultMap="InformationCatalogueResult">
        <include refid="selectInformationCatalogueVo"/>
        where id = #{id}
    </select>


    <select id="selectInformationCatalogueVOById" parameterType="Long" resultMap="InformationCatalogueVOResult">
        SELECT
            ca.id, ca.catalogue_name, ca.parent_id, ca.catalogue_system_code, ca.catalogue_code,
            ca.cooperation_company, ca.cooperation_project, ca.catalogue_type, ca.is_public,
            ca.org_id,ca.dept_id, ca.order_num, ca.remake, ca.create_time, ca.update_time,
            parent.catalogue_name as parent_name,
#             GROUP_CONCAT(DISTINCT sys_dept.dept_id ORDER BY sys_dept.dept_id ASC SEPARATOR ', ') AS au_dept_ids,
#             GROUP_CONCAT(DISTINCT sys_post.post_id ORDER BY sys_post.post_id ASC SEPARATOR ', ') AS au_post_ids,
#             GROUP_CONCAT(DISTINCT sys_user.user_id ORDER BY sys_user.user_id ASC SEPARATOR ', ') AS au_user_ids,
            dept.dept_name as dept_name,
            org.dept_name as org_name,
            user.nick_name as create_by,
            ziic.org_id as parent_org_id
        FROM
            zl_supervise_information_catalogue ca
                left join sys_user user on user.user_name = ca.create_by
#                LEFT JOIN zl_authority au ON ca.id = au.bill_id and au.del_flag = 0 and bill_type = 0
                LEFT JOIN zl_supervise_information_catalogue parent on ca.parent_id = parent.id
#                 LEFT JOIN sys_dept ON au.authority_type = 0 AND au.authority_id = sys_dept.dept_id
#                 LEFT JOIN sys_post ON au.authority_type = 1 AND au.authority_id = sys_post.post_id
#                 LEFT JOIN sys_user ON au.authority_type = 2 AND au.authority_id = sys_user.user_id
                LEFT join sys_dept dept on ca.dept_id = dept.dept_id
                LEFT join sys_dept org on ca.org_id = org.dept_id
                left join zl_supervise_information_catalogue ziic on ziic.id = ca.parent_id
        where  ca.id = #{id}
        GROUP BY
            ca.id;
    </select>

    <insert id="insertInformationCatalogue" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue" useGeneratedKeys="true" keyProperty="id">
        <selectKey keyProperty="id" resultType="Long" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into zl_supervise_information_catalogue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null and catalogueName != ''">catalogue_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="catalogueSystemCode != null">catalogue_system_code,</if>
            <if test="catalogueCode != null">catalogue_code,</if>
            <if test="cooperationCompany != null">cooperation_company,</if>
            <if test="cooperationProject != null">cooperation_project,</if>
            <if test="catalogueType != null">catalogue_type,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="orgId != null">org_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="remake != null">remake,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="catalogueName != null and catalogueName != ''">#{catalogueName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="catalogueSystemCode != null">#{catalogueSystemCode},</if>
            <if test="catalogueCode != null">#{catalogueCode},</if>
            <if test="cooperationCompany != null">#{cooperationCompany},</if>
            <if test="cooperationProject != null">#{cooperationProject},</if>
            <if test="catalogueType != null">#{catalogueType},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="remake != null">#{remake},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateInformationCatalogue" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue">
        update zl_supervise_information_catalogue
        <trim prefix="SET" suffixOverrides=",">
            <if test="catalogueName != null and catalogueName != ''">catalogue_name = #{catalogueName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="catalogueSystemCode != null">catalogue_system_code = #{catalogueSystemCode},</if>
            <if test="catalogueCode != null">catalogue_code = #{catalogueCode},</if>
            <if test="cooperationCompany != null">cooperation_company = #{cooperationCompany},</if>
            <if test="cooperationProject != null">cooperation_project = #{cooperationProject},</if>
            <if test="catalogueType != null">catalogue_type = #{catalogueType},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="remake != null">remake = #{remake},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInformationCatalogueById" parameterType="Long">
        delete from zl_supervise_information_catalogue where id = #{id}
    </delete>

    <delete id="deleteInformationCatalogueByIds" parameterType="String">
        delete from zl_supervise_information_catalogue where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCountByCreateTime" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM zl_supervise_information_catalogue
        <where>
            <if test="createTime != null  and createTime != ''"> and create_time like concat('%', #{createTime}, '%')</if>
        </where>
    </select>

    <select id="selectInformationCatalogueByIds" parameterType="java.util.Set" resultType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue">
        <include refid="selectInformationCatalogueVo"/>
        where id in
        <foreach item="id" collection="catalogueIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectInformationCatalogueByParentId" resultType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue">
        select id, catalogue_name, parent_id, catalogue_system_code, catalogue_code,
               cooperation_company, cooperation_project, catalogue_type, is_public,
               org_id, dept_id, order_num, remake, create_by, create_time, update_by, update_time
        from zl_supervise_information_catalogue
        <where>
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
        </where>
        ORDER BY order_num ASC
    </select>


    <select id="selectInformationCatalogueVOList" resultType="org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue">
        <include refid="selectInformationCatalogueVo"/>
        <where>
            <if test="catalogueName != null  and catalogueName != ''"> and catalogue_name like concat('%', #{catalogueName}, '%')</if>
        </where>
    </select>

    <select id="getCountByCatalogueParentId" resultType="int" parameterType="String">
        SELECT COUNT(id) FROM zl_supervise_information_catalogue
        where parent_id in
        <foreach item="catalogueId" collection="array" open="(" separator="," close=")">
            #{catalogueId}
        </foreach>
    </select>
    <select id="selectInformationCatalogues" resultMap="InformationCatalogueResult">
        select id, catalogue_name, parent_id, catalogue_system_code, catalogue_code,
               cooperation_company, cooperation_project, catalogue_type, is_public,
               org_id, dept_id, order_num, remake, create_by, create_time, update_by, update_time
        from zl_supervise_information_catalogue
    </select>

    <select id="selectBHTCatalogue" resultMap="InformationCatalogueResult">
        select id, catalogue_name, parent_id, catalogue_system_code, catalogue_code,
               cooperation_company, cooperation_project, catalogue_type, is_public,
               org_id, dept_id, order_num, remake, create_by, create_time, update_by, update_time
        from zl_supervise_information_catalogue
        where catalogue_name = 'B类合同资料'
        limit 1
    </select>
<!--    <select id="selectInformationCatalogueListOfAuthority" resultType="org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO" parameterType="org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue">-->
<!--        select ic.id, catalogue_name, parent_id, catalogue_system_code, catalogue_code,-->
<!--               cooperation_company, cooperation_project, catalogue_type, is_public,-->
<!--               org_id, dept_id, order_num, remake, create_by, create_time, update_by, update_time-->
<!--        from zl_supervise_information_catalogue ic-->
<!--        left join-->
<!--        (SELECT zu.bill_id,zu.authority_id,zu.authority_type,zu.bill_type,zu.del_flag FROM zl_authority zu WHERE zu.version = (SELECT MAX(version) FROM zl_authority WHERE bill_id = zu.bill_id)) au ON ic.id = au.bill_id-->
<!--        where bill_type = 0-->
<!--        and ( ic.create_by = #{createBy}-->
<!--        OR  (au.authority_id = #{auDeptId} AND au.authority_type = '0')-->
<!--        OR (au.authority_id = #{auUserId} AND au.authority_type = '2')-->
<!--        <if test="auPostIds != null and auPostIds.size() > 0">-->
<!--            OR (au.authority_id IN-->
<!--            <foreach collection="auPostIds" item="auPostId" separator="," open="(" close=")">-->
<!--                #{auPostId}-->
<!--            </foreach>-->
<!--            AND au.authority_type = '1')-->
<!--        </if>-->
<!--        ) group by ic.id-->
<!--    </select>-->


</mapper>

