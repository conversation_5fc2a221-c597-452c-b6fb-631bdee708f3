package org.ruoyi.core.personnel.mapper;

import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysPostVo;
import org.apache.ibatis.annotations.Mapper;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesExcel;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOrganizationVo;

import java.util.List;

/**
 * 人员档案Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-02
 */
@Mapper
public interface PersonnelArchivesMapper
{
    /**
     * 查询人员档案
     *
     * @param id 人员档案主键
     * @return 人员档案
     */
    public PersonnelArchives selectPersonnelArchivesById(Long id);

    /**
     * 查询人员档案列表
     *
     * @param personnelArchives 人员档案
     * @return 人员档案集合
     */
    public List<PersonnelArchivesVo> selectPersonnelArchivesList(PersonnelArchivesVo personnelArchives);

    /**
     * 新增人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    public int insertPersonnelArchives(PersonnelArchives personnelArchives);

    /**
     * 修改人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    public int updatePersonnelArchives(PersonnelArchives personnelArchives);

    /**
     * 修改人员档案 - 初始化用
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    public int updatePersonnelArchivesForInit(PersonnelArchives personnelArchives);

    /**
     * 删除人员档案
     *
     * @param id 人员档案主键
     * @return 结果
     */
    public int deletePersonnelArchivesById(Long id);

    /**
     * 批量删除人员档案
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonnelArchivesByIds(Long[] ids);

    /**
     * 批量新增
     * @param personnelArchivesList
     */
    public int batchInsert(List<PersonnelArchives> personnelArchivesList);

    public int batchInsertExcel(List<PersonnelArchivesExcel> personnelArchivesList);
    public List<PersonnelArchives> selectListByIdCard(String[] idCards);

    public List<PersonnelArchives> selectListBySysName(String[] sysNames);

    public int getCountByIdCard(String idCard);

    public int getCountByCreateTime(String createTime);

    /**
     * 查询人员档案列表
     *
     * @param personnelArchives 人员档案
     * @return 人员档案集合
     */
    public List<PersonnelArchivesVo> selectPersonnelArchivesListForTransfer(PersonnelArchivesVo personnelArchives);

    /**
     * 查询人员未转正列表
     *
     * @param personnelArchives 人员档案
     * @return 人员档案集合
     */
    public List<PersonnelArchives> getFormalBeforeList(PersonnelArchivesVo personnelArchives);


    public List<PersonnelArchivesVo> listForResignation(PersonnelArchivesVo personnelArchives);

    public int updatePersonnelStateBySysName(PersonnelArchives personnelArchives);

    public List<PersonnelArchivesVo> selectListOfMonthLog();
    /**
    * 仅获取下级的列表
     * @param sysName
     * @return
    */
    public List<PersonnelArchivesVo> getSubordinateList(String sysName);

    /**
     * 获取自己和自己的下级
     * @param userId
     * @return
     */
    public List<PersonnelArchivesVo> subordinateList(Long userId);

    /**
     * 查询当前登录用户的下级、下下级、下下下级...
     * @param userName
     * @return
     */
    PersonnelArchivesVo queryPersonnelOrganization(String userName);

    /**
     * 查询用户的上级
     */
    PersonnelArchivesVo queryParentPersonnelBySysName(String userName);

    /**
     * 根据人员id查询人员的主岗位
     * @param id
     * @return
     */
    SysPostVo selectPerHomePostById(Long id);

    PersonnelArchivesVo selectPersonnelArchivesByName(String userName);

    PersonnelArchivesVo selectPersonnelArchivesInfoBysysName(String sysName);
}
