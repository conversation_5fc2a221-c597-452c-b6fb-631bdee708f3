package org.ruoyi.core.superviseInformation.service;

import org.ruoyi.core.superviseInformation.domain.SuperviseInformationCatalogue;
import org.ruoyi.core.superviseInformation.domain.vo.SuperviseInformationCatalogueVO;

import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * 资料目录Service接口
 *
 * <AUTHOR>
 * @date 2023-11-10
 *
 */
public interface ISuperviseInformationCatalogueService
{
    /**
     * 查询资料目录
     *
     * @param id 资料目录主键
     * @return 资料目录
     */
    public SuperviseInformationCatalogue selectInformationCatalogueById(Long id);

    public SuperviseInformationCatalogueVO selectInformationCatalogueVOById(Long id);

    /**
     * 查询资料目录列表
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录集合
     */
    public List<SuperviseInformationCatalogueVO> selectInformationCatalogueList(SuperviseInformationCatalogueVO informationCatalogue);

    public List<SuperviseInformationCatalogueVO> selectInformationCatalogueListNoAuthority(SuperviseInformationCatalogueVO informationCatalogue);
    /**
     * 查询资料目录列表 没有分页
     *
     * @param informationCatalogue 资料目录
     * @return 资料目录集合
     */
    public List<SuperviseInformationCatalogueVO> selectInformationCatalogueListNoLimit(SuperviseInformationCatalogueVO informationCatalogue);

    /**
     * 新增资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int insertInformationCatalogue(SuperviseInformationCatalogue informationCatalogue) throws ParseException;

    /**
     * 新增b类合同资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public Long insertInformationCatalogueBHT(SuperviseInformationCatalogue informationCatalogue);

    /**
     * 修改资料目录
     *
     * @param informationCatalogue 资料目录
     * @return 结果
     */
    public int updateInformationCatalogue(SuperviseInformationCatalogue informationCatalogue) throws ParseException;

    /**
     * 批量删除资料目录
     *
     * @param ids 需要删除的资料目录主键集合
     * @return 结果
     */
    public int deleteInformationCatalogueByIds(Long[] ids);

    /**
     * 删除资料目录信息
     *
     * @param id 资料目录主键
     * @return 结果
     */
    public int deleteInformationCatalogueById(Long id);

    /**
     * 查询数量
     * @param createTime
     * @return
     */
    int getCountByCreateTime(String createTime);

    public List<SuperviseInformationCatalogue> selectInformationCatalogueByParentId(Long parentId);



    public List<SuperviseInformationCatalogue> selectInformationCatalogueByIds(Set<Long> catalogueIds);

    public List<SuperviseInformationCatalogueVO> getTreeListOfAuthority(SuperviseInformationCatalogueVO informationCatalogue);

    public List<SuperviseInformationCatalogueVO> selectCatalogueListOfAuthority(SuperviseInformationCatalogue informationCatalogue);

}
