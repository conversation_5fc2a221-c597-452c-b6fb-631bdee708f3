package org.ruoyi.core.debtConversion.domain.vo;

import lombok.Data;
import org.ruoyi.core.debtConversion.domain.DebtConversion;
import org.ruoyi.core.debtConversion.domain.DebtConversionFile;

import java.util.Date;
import java.util.List;

@Data
public class DebtConversionFileVo extends DebtConversionFile {

    private String companyShortName;

    private Date startCreateTime;

    private Date endCreateTime;

    private String  createByName;

    private List<DebtConversion> successList;
    /**
     * 子表数量
     */
    private Long conversionCount;
}
